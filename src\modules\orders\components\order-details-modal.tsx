import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { formatCurrency } from "@/shared/lib/format-currency";
import { formatDateTime } from "@/shared/lib/format-date";
import { getOrderStatusConfig } from "@/shared/utils/order-status-config";
import { motion } from "framer-motion";
import { Calendar, CreditCard, FileText, Package, Receipt, ShoppingCart, Truck, User, X } from "lucide-react";
import { useOrderFindDetailedById } from "../hooks/find-detailed-by-id.hook";

interface OrderDetailsModalProps {
	isOpen: boolean;
	onClose: () => void;
	orderId: number | null;
}

export const OrderDetailsModal = ({ isOpen, onClose, orderId }: OrderDetailsModalProps) => {
	const { data: orderData, isLoading, error } = useOrderFindDetailedById(orderId || 0);

	const order = orderData?.success ? orderData.data : null;

	if (!isOpen || !orderId) return null;

	const statusConfig = order ? getOrderStatusConfig(order.status) : null;
	const StatusIcon = statusConfig?.icon;

	return (
		<OverlayContainer isVisible={isOpen} onClose={onClose}>
			<motion.dialog
				onClick={e => e.stopPropagation()}
				open={isOpen}
				aria-label="Detalhes do Pedido"
				className="flex flex-col w-full max-w-4xl max-h-[90vh] bg-white rounded-[15px] shadow-lg overflow-hidden"
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2, ease: "easeOut" }}
			>
				{/* Header */}
				<div className="flex items-center justify-between p-4 md:p-6 border-b border-gray-200 bg-gray-50">
					<div className="flex items-center gap-3">
						<div className="bg-mainColor/10 p-2 rounded-full">
							<ShoppingCart size={20} className="text-mainColor" />
						</div>
						<div>
							<h2 className="text-lg md:text-xl font-semibold text-gray-800">Pedido #{orderId}</h2>
							{order && statusConfig && (
								<div className="flex items-center gap-2 mt-1">
									<span
										className={`inline-flex items-center gap-1 px-2 py-1 rounded-xl text-xs font-semibold ${statusConfig.color}`}
									>
										{StatusIcon && <StatusIcon size={12} />}
										{statusConfig.label}
									</span>
								</div>
							)}
						</div>
					</div>
					<button onClick={onClose} className="p-2 hover:bg-gray-200 rounded-full transition-colors" aria-label="Fechar modal">
						<X size={20} className="text-gray-500" />
					</button>
				</div>

				{/* Content */}
				<div className="flex-1 overflow-y-auto p-4 md:p-6">
					{isLoading ? (
						<div className="flex items-center justify-center py-12">
							<div className="flex flex-col items-center gap-3">
								<motion.div
									className="w-8 h-8 border-2 border-mainColor border-t-transparent rounded-full"
									animate={{ rotate: 360 }}
									transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
								/>
								<span className="text-gray-500 text-sm">Carregando detalhes...</span>
							</div>
						</div>
					) : error || !order ? (
						<div className="flex items-center justify-center py-12">
							<div className="text-center">
								<div className="bg-red-100 p-3 rounded-full w-fit mx-auto mb-3">
									<X size={24} className="text-red-500" />
								</div>
								<h3 className="text-lg font-medium text-gray-800 mb-2">Erro ao carregar</h3>
								<p className="text-gray-500 text-sm">Não foi possível carregar os detalhes do pedido.</p>
							</div>
						</div>
					) : (
						<div className="space-y-6">
							{/* Customer & Financial Summary */}
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								{/* Customer Info */}
								<div className="bg-gray-50 rounded-lg p-4">
									<div className="flex items-center gap-2 mb-3">
										<User size={16} className="text-gray-500" />
										<h3 className="font-medium text-gray-800">Cliente</h3>
									</div>
									<div className="space-y-2">
										<p className="font-medium text-gray-900">{order.customer}</p>
										{order.customerCpfCnpj && <p className="text-sm text-gray-600">{order.customerCpfCnpj}</p>}
									</div>
								</div>

								{/* Financial Summary */}
								<div className="bg-gray-50 rounded-lg p-4">
									<div className="flex items-center gap-2 mb-3">
										<Receipt size={16} className="text-gray-500" />
										<h3 className="font-medium text-gray-800">Resumo Financeiro</h3>
									</div>
									<div className="space-y-2 text-sm">
										<div className="flex justify-between">
											<span className="text-gray-600">Subtotal:</span>
											<span className="font-medium">{formatCurrency(Number(order.subtotal))}</span>
										</div>
										{Number(order.shippingCost) > 0 && (
											<div className="flex justify-between">
												<span className="text-gray-600">Frete:</span>
												<span className="font-medium">{formatCurrency(Number(order.shippingCost))}</span>
											</div>
										)}
										{Number(order.discount) > 0 && (
											<div className="flex justify-between">
												<span className="text-gray-600">Desconto:</span>
												<span className="font-medium text-red-600">-{formatCurrency(Number(order.discount))}</span>
											</div>
										)}
										<div className="flex justify-between pt-2 border-t border-gray-200">
											<span className="font-medium text-gray-800">Total:</span>
											<span className="font-bold text-green-600 text-lg">{formatCurrency(Number(order.total))}</span>
										</div>
									</div>
								</div>
							</div>

							{/* Salesperson */}
							{order.salesperson && (
								<div className="bg-blue-50 rounded-lg p-4">
									<div className="flex items-center gap-2 mb-2">
										<User size={16} className="text-blue-600" />
										<h3 className="font-medium text-gray-800">Vendedor</h3>
									</div>
									<p className="text-gray-900">{order.salesperson}</p>
								</div>
							)}

							{/* Items */}
							<div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
								<div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
									<div className="flex items-center gap-2">
										<Package size={16} className="text-gray-500" />
										<h3 className="font-medium text-gray-800">Itens do Pedido</h3>
										<span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
											{order.items.length} {order.items.length === 1 ? "item" : "itens"}
										</span>
									</div>
								</div>
								<div className="divide-y divide-gray-200">
									{order.items.map((item, index) => (
										<div key={item.id} className="p-4">
											<div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
												<div className="flex-1">
													<h4 className="font-medium text-gray-900 mb-1">{item.product}</h4>
													<div className="flex items-center gap-4 text-sm text-gray-600">
														<span>Qtd: {item.quantity}</span>
														<span>Preço: {formatCurrency(item.price)}</span>
														{item.discount > 0 && (
															<span className="text-red-600">Desc: {formatCurrency(item.discount)}</span>
														)}
													</div>
												</div>
												<div className="text-right">
													<span className="font-medium text-gray-900">{item.total}</span>
												</div>
											</div>
										</div>
									))}
								</div>
							</div>

							{/* Payments */}
							{order.payments && order.payments.length > 0 && (
								<div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
									<div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
										<div className="flex items-center gap-2">
											<CreditCard size={16} className="text-gray-500" />
											<h3 className="font-medium text-gray-800">Pagamentos</h3>
											<span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">{order.payments.length}</span>
										</div>
									</div>
									<div className="divide-y divide-gray-200">
										{order.payments.map((payment, index) => (
											<div key={payment.id} className="p-4">
												<div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
													<div className="flex-1">
														<h4 className="font-medium text-gray-900 mb-1">{payment.paymentMethod}</h4>
														{payment.parcel > 1 && (
															<div className="text-sm text-gray-600">Parcela: {payment.parcel}x</div>
														)}
													</div>
													<div className="text-right">
														<span className="font-medium text-green-600">{payment.value}</span>
													</div>
												</div>
											</div>
										))}
									</div>
								</div>
							)}

							{/* Invoice */}
							{order.invoice && order.invoice.length > 0 && (
								<div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
									<div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
										<div className="flex items-center gap-2">
											<FileText size={16} className="text-gray-500" />
											<h3 className="font-medium text-gray-800">Notas Fiscais</h3>
											<span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">{order.invoice.length}</span>
										</div>
									</div>
									<div className="divide-y divide-gray-200">
										{order.invoice.map((invoice, index) => (
											<div key={invoice.id} className="p-4">
												<div className="space-y-3">
													<div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
														<div className="flex-1">
															<h4 className="font-medium text-gray-900 mb-1">NF #{invoice.sequence}</h4>
															<div className="flex items-center gap-4 text-sm text-gray-600">
																<span>Tipo: {invoice.type}</span>
																<span>Status: {invoice.status}</span>
																{invoice.issueDate && <span>Emissão: {formatDateTime(invoice.issueDate)}</span>}
															</div>
														</div>
													</div>

													{invoice.key && (
														<div className="bg-gray-50 rounded p-3">
															<div className="text-xs text-gray-600 mb-1">Chave de Acesso:</div>
															<div className="font-mono text-sm text-gray-800 break-all">{invoice.key}</div>
														</div>
													)}

													{invoice.supplier && (
														<div className="text-sm">
															<span className="text-gray-600">Fornecedor: </span>
															<span className="text-gray-900">{invoice.supplier}</span>
														</div>
													)}

													{/* Events */}
													{invoice.events && invoice.events.length > 0 && (
														<div className="mt-3">
															<h5 className="text-sm font-medium text-gray-700 mb-2">Eventos:</h5>
															<div className="space-y-2">
																{invoice.events.map((event, eventIndex) => (
																	<div key={event.id} className="bg-gray-50 rounded p-2 text-sm">
																		<div className="flex items-center gap-2 mb-1">
																			<Calendar size={12} className="text-gray-500" />
																			<span className="text-gray-600">{formatDateTime(event.date)}</span>
																		</div>
																		<p className="text-gray-800">{event.reason}</p>
																	</div>
																))}
															</div>
														</div>
													)}
												</div>
											</div>
										))}
									</div>
								</div>
							)}
						</div>
					)}
				</div>
			</motion.dialog>
		</OverlayContainer>
	);
};
